import React, { useState, useRef } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Dimensions,
} from "react-native";
import { useRouter } from "expo-router";
import { CameraView, CameraType, useCameraPermissions } from "expo-camera";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { useMeQuery, useIndexFaceMutation } from "@/generated/graphql";
import { StatusBar } from "expo-status-bar";

const { width, height } = Dimensions.get("window");

export default function FaceRegistrationScreen() {
  const router = useRouter();
  const cameraRef = useRef<CameraView>(null);
  const [showCamera, setShowCamera] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [permission, requestPermission] = useCameraPermissions();
  const { data: me, refetch: refetchMe } = useMeQuery();
  const { mutateAsync: indexFace } = useIndexFaceMutation();

  // Check if user already has face registered
  const hasFaceRegistered = !!me?.me?.faceInformation?.faceId;

  const handleStartRegistration = async () => {
    if (!permission?.granted) {
      const result = await requestPermission();
      if (!result.granted) {
        Alert.alert(
          "Camera Permission Required",
          "We need camera access to register your face for attendance tracking.",
          [{ text: "OK" }]
        );
        return;
      }
    }
    setShowCamera(true);
  };

  const takePicture = async () => {
    if (!cameraRef.current) return;

    try {
      setIsLoading(true);
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: true,
      });

      if (photo?.base64) {
        await handleFaceRegistration(photo.base64);
      }
    } catch (error) {
      console.error("Error taking picture:", error);
      Alert.alert("Error", "Failed to capture photo. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleFaceRegistration = async (base64Image: string) => {
    try {
      setIsLoading(true);

      if (!me?.me?.id) {
        throw new Error("User ID not found");
      }

      await indexFace({
        indexFaceInput: {
          base64Img: base64Image,
          userId: me.me.id,
        },
      });

      Alert.alert(
        "Success!",
        "Your face has been successfully registered for AI attendance tracking.",
        [
          {
            text: "OK",
            onPress: () => {
              setShowCamera(false);
              refetchMe(); // Refresh user data
              router.back();
            },
          },
        ]
      );
    } catch (error) {
      console.error("Face registration failed:", error);
      Alert.alert(
        "Registration Failed",
        "Failed to register your face. Please try again.",
        [{ text: "OK" }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (hasFaceRegistered) {
    return (
      <View style={styles.container}>
        <StatusBar style="light" />
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Face Registration</Text>
        </View>

        <View style={styles.content}>
          <View style={styles.successContainer}>
            <Ionicons
              name="checkmark-circle"
              size={80}
              color={Colors.success}
            />
            <Text style={styles.successTitle}>Face Already Registered</Text>
            <Text style={styles.successMessage}>
              Your face is already registered for AI attendance tracking. You
              can now use face recognition for quick and secure attendance
              marking.
            </Text>
          </View>
        </View>
      </View>
    );
  }

  if (showCamera) {
    return (
      <View style={styles.cameraContainer}>
        <StatusBar style="light" />
        <CameraView
          style={styles.camera}
          ref={cameraRef}
          facing="front"
          mute={false}
        >
          <View style={styles.cameraOverlay}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowCamera(false)}
            >
              <Ionicons name="close" size={32} color="white" />
            </TouchableOpacity>

            <View style={styles.instructionContainer}>
              <Text style={styles.cameraInstruction}>
                Position your face within the frame
              </Text>
              <Text style={styles.cameraSubInstruction}>
                Look directly at the camera and ensure good lighting
              </Text>
            </View>

            <View style={styles.faceFrame} />

            <View style={styles.captureContainer}>
              <TouchableOpacity
                style={[
                  styles.captureButton,
                  isLoading && styles.captureButtonDisabled,
                ]}
                onPress={takePicture}
                disabled={isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator size="large" color={Colors.white} />
                ) : (
                  <View style={styles.captureButtonInner} />
                )}
              </TouchableOpacity>
            </View>
          </View>
        </CameraView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Face Registration</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Ionicons name="scan-sharp" size={80} color={Colors.primary} />
        </View>

        <Text style={styles.title}>Register Your Face</Text>
        <Text style={styles.subtitle}>
          Enable AI-powered attendance tracking for quick and secure check-ins
        </Text>

        <View style={styles.benefitsContainer}>
          <View style={styles.benefitItem}>
            <Ionicons name="flash" size={24} color={Colors.primary} />
            <Text style={styles.benefitText}>Quick attendance marking</Text>
          </View>
          <View style={styles.benefitItem}>
            <Ionicons
              name="shield-checkmark"
              size={24}
              color={Colors.primary}
            />
            <Text style={styles.benefitText}>Secure and accurate</Text>
          </View>
          <View style={styles.benefitItem}>
            <Ionicons name="time" size={24} color={Colors.primary} />
            <Text style={styles.benefitText}>Save time on check-ins</Text>
          </View>
        </View>

        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsTitle}>For best results:</Text>
          <Text style={styles.instructionItem}>• Ensure good lighting</Text>
          <Text style={styles.instructionItem}>
            • Look directly at the camera
          </Text>
          <Text style={styles.instructionItem}>• Keep your face centered</Text>
          <Text style={styles.instructionItem}>
            • Remove glasses if possible
          </Text>
        </View>

        <TouchableOpacity
          style={styles.startButton}
          onPress={handleStartRegistration}
        >
          <Ionicons name="camera" size={20} color={Colors.white} />
          <Text style={styles.startButtonText}>Start Registration</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: Colors.white,
  },
  content: {
    flex: 1,
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: `${Colors.primary}15`,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: Colors.text,
    textAlign: "center",
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
    marginBottom: 32,
    lineHeight: 24,
  },
  benefitsContainer: {
    width: "100%",
    marginBottom: 32,
  },
  benefitItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  benefitText: {
    fontSize: 16,
    color: Colors.text,
    marginLeft: 12,
  },
  instructionsContainer: {
    width: "100%",
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 20,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
    marginBottom: 12,
  },
  instructionItem: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 8,
    lineHeight: 20,
  },
  startButton: {
    backgroundColor: Colors.primary,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    width: "100%",
  },
  startButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.white,
    marginLeft: 8,
  },
  // Camera styles
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.3)",
  },
  closeButton: {
    position: "absolute",
    top: 50,
    left: 20,
    zIndex: 10,
  },
  instructionContainer: {
    position: "absolute",
    top: 100,
    left: 20,
    right: 20,
    alignItems: "center",
  },
  cameraInstruction: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.white,
    textAlign: "center",
    marginBottom: 8,
  },
  cameraSubInstruction: {
    fontSize: 14,
    color: Colors.white,
    textAlign: "center",
    opacity: 0.9,
  },
  faceFrame: {
    position: "absolute",
    top: height * 0.25,
    left: width * 0.15,
    width: width * 0.7,
    height: width * 0.7,
    borderWidth: 3,
    borderColor: Colors.white,
    borderRadius: width * 0.35,
    opacity: 0.8,
  },
  captureContainer: {
    position: "absolute",
    bottom: 100,
    left: 0,
    right: 0,
    alignItems: "center",
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "rgba(255,255,255,0.3)",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 4,
    borderColor: Colors.white,
  },
  captureButtonDisabled: {
    opacity: 0.6,
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.white,
  },
  // Success styles
  successContainer: {
    alignItems: "center",
  },
  successTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.text,
    marginTop: 20,
    marginBottom: 16,
  },
  successMessage: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
    lineHeight: 24,
  },
});
