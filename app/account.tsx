import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
} from "react-native";
import { useRouter } from "expo-router";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { useSession } from "@/providers/auth-provider";
import { useMeQuery } from "@/generated/graphql";

// Define types for the AccountOption component
interface AccountOptionProps {
  icon: keyof typeof Ionicons.glyphMap;
  label: string;
  onPress: () => void;
  disabled?: boolean;
}

// Account option item component
function AccountOption({
  icon,
  label,
  onPress,
  disabled = false,
}: AccountOptionProps) {
  return (
    <TouchableOpacity
      style={[styles.optionItem, disabled && styles.optionItemDisabled]}
      onPress={onPress}
      activeOpacity={disabled ? 1 : 0.7}
      disabled={disabled}
    >
      <View
        style={[
          styles.optionIconContainer,
          disabled && styles.optionIconContainerDisabled,
        ]}
      >
        <Ionicons
          name={icon}
          size={22}
          color={disabled ? Colors.textLight : Colors.primary}
        />
      </View>
      <Text
        style={[styles.optionLabel, disabled && styles.optionLabelDisabled]}
      >
        {label}
      </Text>
      <Ionicons
        name="chevron-forward"
        size={20}
        color={disabled ? Colors.border : Colors.textLight}
      />
    </TouchableOpacity>
  );
}

export default function AccountScreen() {
  const router = useRouter();
  const { signOut } = useSession();
  const { data: me } = useMeQuery();

  // Handle option press
  const handleOptionPress = (optionName: string) => {
    // Navigate based on the option selected
    if (optionName === "Incident Reporting") {
      // Use type assertion to fix TypeScript error
      // This is safe because we've added the screen to _layout.tsx
      router.push("/incident-reporting" as any);
    } else if (optionName === "Uniform Request") {
      router.push("/uniform-request" as any);
    } else if (optionName === "Claims") {
      router.push("/claims" as any);
    } else if (optionName === "Register Face") {
      alert("Face registration is not implemented yet");
      router.push("/face-registration" as any);
    } else if (optionName === "Sign Out") {
      Alert.alert("Sign Out", "Are you sure you want to sign out?", [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Sign Out",
          onPress: () => {
            signOut();
            router.replace("/login");
          },
          style: "destructive",
        },
      ]);
    }
    // Add more navigation options as needed
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* User account section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>User account</Text>
        <View style={styles.optionsList}>
          <AccountOption
            icon="person-outline"
            label="Personal Information"
            onPress={() => handleOptionPress("Personal Information")}
          />
          <AccountOption
            icon="warning-outline"
            label="Incident Reporting"
            onPress={() => handleOptionPress("Incident Reporting")}
          />
          <AccountOption
            icon="shirt-outline"
            label="Uniform Request"
            onPress={() => handleOptionPress("Uniform Request")}
          />
          <AccountOption
            icon="cash-outline"
            label="Claims"
            onPress={() => handleOptionPress("Claims")}
          />
          <AccountOption
            icon="help-circle-outline"
            label="Help & Support"
            onPress={() => handleOptionPress("Help & Support")}
          />
          <AccountOption
            disabled={!!me?.me?.faceInformation?.faceId}
            icon="scan-sharp"
            label={
              me?.me?.faceInformation?.faceId
                ? "Face Registered"
                : "Register Face"
            }
            onPress={() => handleOptionPress("Register Face")}
          />
          <AccountOption
            icon="log-out-outline"
            label="Sign Out"
            onPress={() => handleOptionPress("Sign Out")}
          />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  optionsList: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    marginHorizontal: 16,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: Colors.border,
  },
  optionItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  optionIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: `${Colors.primary}15`,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  optionLabel: {
    flex: 1,
    fontSize: 16,
    color: Colors.text,
  },
  optionItemDisabled: {
    opacity: 0.6,
  },
  optionIconContainerDisabled: {
    backgroundColor: `${Colors.textLight}15`,
  },
  optionLabelDisabled: {
    color: Colors.textLight,
  },
});
