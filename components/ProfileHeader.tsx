import React from "react";
import { StyleSheet, View, Text, Image } from "react-native";
import { Colors } from "@/constants/Colors";
import { hexToRGBA } from "@/constants/Colors";
import { extractInitials } from "@/lib/utils";

interface ProfileHeaderProps {
  name: string;
  role: string;
  avatarUrl?: string;
}

/**
 * Profile header component with user info and avatar
 */
export function ProfileHeader({ name, role, avatarUrl }: ProfileHeaderProps) {
  return (
    <View style={styles.container}>
      <View style={styles.backgroundShape} />

      <View style={styles.content}>
        <View style={styles.textContainer}>
          <Text style={styles.name}>{name}</Text>
        </View>

        <View style={styles.avatarContainer}>
          {avatarUrl ? (
            <Image
              source={{ uri: avatarUrl }}
              style={styles.avatar}
              resizeMode="cover"
            />
          ) : extractInitials(name) ? (
            <View style={styles.avatar}>
              <Text style={styles.initials}>{extractInitials(name)}</Text>
            </View>
          ) : null}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    position: "relative",
    overflow: "hidden",
  },
  backgroundShape: {
    position: "absolute",
    top: -100,
    right: -100,
    width: 300,
    height: 300,
    borderRadius: 150,
    backgroundColor: hexToRGBA(Colors.primaryLight, 0.3),
    zIndex: 0, // Ensure proper rendering on Android
  },
  content: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textContainer: {
    flex: 1,
  },
  name: {
    fontSize: 28,
    fontWeight: "bold",
    color: Colors.white,
    marginBottom: 4,
    textTransform: "capitalize",
  },

  avatarContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: Colors.white,
    overflow: "hidden",
  },
  avatar: {
    width: "100%",
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
  },
  initials: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.white,
    textAlign: "center",
  },
});
